using System;
using System.Text.Json;
using System.Threading.Tasks;
using Azure.Messaging.ServiceBus;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Publisher.Customer.Api.Domain;
using Publisher.Customer.Api.Messages;

namespace Publisher.Customer.Api.Infrastructure
{
    public class ServiceBusCustomerMessagePublisher : ICustomerMessagePublisher, IAsyncDisposable
    {
        private readonly ServiceBusClient _serviceBusClient;
        private readonly ServiceBusSender _sender;
        private readonly ILogger<ServiceBusCustomerMessagePublisher> _logger;
        private readonly string _topicName;

        public ServiceBusCustomerMessagePublisher(
            IConfiguration configuration,
            ILogger<ServiceBusCustomerMessagePublisher> logger)
        {
            _logger = logger;
            
            var connectionString = configuration.GetConnectionString("ServiceBus");
            _topicName = configuration["ServiceBus:CustomerTopicName"] ?? "customers";
            
            if (string.IsNullOrEmpty(connectionString))
            {
                _logger.LogWarning("Service Bus connection string not configured. Using mock implementation.");
                // For demo purposes, we'll create a mock client
                _serviceBusClient = null!;
                _sender = null!;
            }
            else
            {
                _serviceBusClient = new ServiceBusClient(connectionString);
                _sender = _serviceBusClient.CreateSender(_topicName);
            }
        }

        public async Task<Result> PublishCustomerCreatedAsync(CustomerCreatedMessage message)
        {
            return await PublishMessageAsync(message, "CustomerCreated");
        }

        public async Task<Result> PublishCustomerUpdatedAsync(CustomerUpdatedMessage message)
        {
            return await PublishMessageAsync(message, "CustomerUpdated");
        }

        public async Task<Result> PublishCustomerDeletedAsync(CustomerDeletedMessage message)
        {
            return await PublishMessageAsync(message, "CustomerDeleted");
        }

        private async Task<Result> PublishMessageAsync<T>(T message, string messageType) where T : CustomerMessage
        {
            try
            {
                if (_sender == null)
                {
                    // Mock implementation for demo
                    _logger.LogInformation("Mock: Publishing {MessageType} message for customer {CustomerId}", 
                        messageType, message.CustomerId);
                    
                    var json = JsonSerializer.Serialize(message, new JsonSerializerOptions 
                    { 
                        PropertyNamingPolicy = JsonNamingPolicy.CamelCase 
                    });
                    
                    _logger.LogInformation("Mock: Message content: {MessageContent}", json);
                    return Result.Success();
                }

                var messageBody = JsonSerializer.Serialize(message, new JsonSerializerOptions 
                { 
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase 
                });

                var serviceBusMessage = new ServiceBusMessage(messageBody)
                {
                    MessageId = Guid.NewGuid().ToString(),
                    CorrelationId = message.CorrelationId,
                    Subject = messageType,
                    ContentType = "application/json"
                };

                // Add custom properties
                serviceBusMessage.ApplicationProperties.Add("MessageType", messageType);
                serviceBusMessage.ApplicationProperties.Add("CustomerId", message.CustomerId);
                serviceBusMessage.ApplicationProperties.Add("Timestamp", message.Timestamp);

                await _sender.SendMessageAsync(serviceBusMessage);

                _logger.LogInformation("Successfully published {MessageType} message for customer {CustomerId} to topic {TopicName}", 
                    messageType, message.CustomerId, _topicName);

                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to publish {MessageType} message for customer {CustomerId}", 
                    messageType, message.CustomerId);
                
                return Result.Failure($"Failed to publish {messageType} message: {ex.Message}");
            }
        }

        public async ValueTask DisposeAsync()
        {
            if (_sender != null)
            {
                await _sender.DisposeAsync();
            }
            
            if (_serviceBusClient != null)
            {
                await _serviceBusClient.DisposeAsync();
            }
        }
    }
}
