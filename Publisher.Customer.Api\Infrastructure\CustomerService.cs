using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Publisher.Customer.Api.Domain;
using Publisher.Customer.Api.Messages;

namespace Publisher.Customer.Api.Infrastructure
{
    public class CustomerService : ICustomerService
    {
        private readonly ICustomerMessagePublisher _messagePublisher;
        private readonly ILogger<CustomerService> _logger;
        
        // In-memory storage for demo purposes (in real scenario, this would be replaced by a read model or cache)
        private static readonly List<Domain.Customer> _customers = new();
        private static int _nextId = 1;

        public CustomerService(
            ICustomerMessagePublisher messagePublisher,
            ILogger<CustomerService> logger)
        {
            _messagePublisher = messagePublisher;
            _logger = logger;
        }

        public async Task<Result<Domain.Customer>> CreateCustomerAsync(Domain.Customer customer)
        {
            try
            {
                _logger.LogInformation("Creating customer with email {Email}", customer.Email);

                // Check for duplicate email
                if (_customers.Any(c => c.Email.Equals(customer.Email, StringComparison.OrdinalIgnoreCase)))
                {
                    _logger.LogWarning("Attempt to create customer with existing email {Email}", customer.Email);
                    return Result.Failure<Domain.Customer>("A customer with this email already exists");
                }

                // Assign ID and timestamps
                customer.Id = _nextId++;
                customer.CreatedAt = DateTime.UtcNow;
                customer.UpdatedAt = DateTime.UtcNow;

                // Create and publish message
                var message = new CustomerCreatedMessage
                {
                    CustomerId = customer.Id,
                    FirstName = customer.FirstName,
                    LastName = customer.LastName,
                    Email = customer.Email,
                    Phone = customer.Phone,
                    CreatedAt = customer.CreatedAt
                };

                var publishResult = await _messagePublisher.PublishCustomerCreatedAsync(message);
                if (publishResult.IsFailure)
                {
                    _logger.LogError("Failed to publish customer created message: {Error}", publishResult.Error);
                    return Result.Failure<Domain.Customer>($"Failed to publish customer created event: {publishResult.Error}");
                }

                // Add to in-memory storage (in real scenario, this would be handled by an event handler)
                _customers.Add(customer);

                _logger.LogInformation("Customer created successfully with ID {CustomerId}", customer.Id);
                return Result.Success(customer);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating customer with email {Email}", customer.Email);
                return Result.Failure<Domain.Customer>("An error occurred while creating the customer");
            }
        }

        public async Task<Result<Domain.Customer>> UpdateCustomerAsync(int id, Domain.Customer updatedCustomer)
        {
            try
            {
                _logger.LogInformation("Updating customer with ID {CustomerId}", id);

                var existingCustomer = _customers.FirstOrDefault(c => c.Id == id);
                if (existingCustomer == null)
                {
                    _logger.LogWarning("Customer with ID {CustomerId} not found", id);
                    return Result.Failure<Domain.Customer>($"Customer with ID {id} not found");
                }

                // Check for duplicate email (excluding current customer)
                if (_customers.Any(c => c.Id != id && c.Email.Equals(updatedCustomer.Email, StringComparison.OrdinalIgnoreCase)))
                {
                    _logger.LogWarning("Attempt to update customer {CustomerId} with existing email {Email}", id, updatedCustomer.Email);
                    return Result.Failure<Domain.Customer>("A customer with this email already exists");
                }

                // Update customer
                existingCustomer.Update(
                    updatedCustomer.FirstName,
                    updatedCustomer.LastName,
                    updatedCustomer.Email,
                    updatedCustomer.Phone
                );

                // Create and publish message
                var message = new CustomerUpdatedMessage
                {
                    CustomerId = existingCustomer.Id,
                    FirstName = existingCustomer.FirstName,
                    LastName = existingCustomer.LastName,
                    Email = existingCustomer.Email,
                    Phone = existingCustomer.Phone,
                    UpdatedAt = existingCustomer.UpdatedAt
                };

                var publishResult = await _messagePublisher.PublishCustomerUpdatedAsync(message);
                if (publishResult.IsFailure)
                {
                    _logger.LogError("Failed to publish customer updated message: {Error}", publishResult.Error);
                    return Result.Failure<Domain.Customer>($"Failed to publish customer updated event: {publishResult.Error}");
                }

                _logger.LogInformation("Customer updated successfully with ID {CustomerId}", id);
                return Result.Success(existingCustomer);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating customer with ID {CustomerId}", id);
                return Result.Failure<Domain.Customer>("An error occurred while updating the customer");
            }
        }

        public async Task<Result> DeleteCustomerAsync(int id)
        {
            try
            {
                _logger.LogInformation("Deleting customer with ID {CustomerId}", id);

                var customer = _customers.FirstOrDefault(c => c.Id == id);
                if (customer == null)
                {
                    _logger.LogWarning("Attempt to delete non-existent customer with ID {CustomerId}", id);
                    return Result.Failure($"Customer with ID {id} not found");
                }

                // Create and publish message
                var message = new CustomerDeletedMessage
                {
                    CustomerId = customer.Id,
                    FirstName = customer.FirstName,
                    LastName = customer.LastName,
                    Email = customer.Email,
                    Phone = customer.Phone,
                    DeletedAt = DateTime.UtcNow
                };

                var publishResult = await _messagePublisher.PublishCustomerDeletedAsync(message);
                if (publishResult.IsFailure)
                {
                    _logger.LogError("Failed to publish customer deleted message: {Error}", publishResult.Error);
                    return Result.Failure($"Failed to publish customer deleted event: {publishResult.Error}");
                }

                // Remove from in-memory storage
                _customers.Remove(customer);

                _logger.LogInformation("Customer deleted successfully with ID {CustomerId}", id);
                return Result.Success();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting customer with ID {CustomerId}", id);
                return Result.Failure("An error occurred while deleting the customer");
            }
        }

        public Task<Result<Domain.Customer>> GetCustomerByIdAsync(int id)
        {
            try
            {
                _logger.LogInformation("Retrieving customer with ID {CustomerId}", id);

                var customer = _customers.FirstOrDefault(c => c.Id == id);
                if (customer == null)
                {
                    _logger.LogWarning("Customer with ID {CustomerId} not found", id);
                    return Task.FromResult(Result.Failure<Domain.Customer>($"Customer with ID {id} not found"));
                }

                _logger.LogInformation("Customer retrieved successfully with ID {CustomerId}", id);
                return Task.FromResult(Result.Success(customer));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving customer with ID {CustomerId}", id);
                return Task.FromResult(Result.Failure<Domain.Customer>("An error occurred while retrieving the customer"));
            }
        }

        public Task<Result<IEnumerable<Domain.Customer>>> GetAllCustomersAsync()
        {
            try
            {
                _logger.LogInformation("Retrieving all customers");

                var customers = _customers
                    .OrderBy(c => c.LastName)
                    .ThenBy(c => c.FirstName)
                    .ToList();

                _logger.LogInformation("Retrieved {CustomerCount} customers", customers.Count);
                return Task.FromResult(Result.Success<IEnumerable<Domain.Customer>>(customers));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all customers");
                return Task.FromResult(Result.Failure<IEnumerable<Domain.Customer>>("An error occurred while retrieving customers"));
            }
        }
    }
}
