using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Publisher.Customer.Api.Domain;
using Publisher.Customer.Api.Infrastructure;

namespace Publisher.Customer.Api.Features.Customers.GetCustomer
{
    public class GetCustomerHandler : BaseCustomerHandler<GetCustomerHandler>
    {
        public GetCustomerHandler(ICustomerService customerService, ILogger<GetCustomerHandler> logger)
            : base(customerService, logger)
        {
        }

        public async Task<Result<GetCustomerResponse>> HandleAsync(int id)
        {
            LogStart("Retrieving customer with ID {CustomerId}", id);

            var result = await CustomerService.GetCustomerByIdAsync(id);

            if (result.IsFailure)
            {
                return HandleFailure<GetCustomerResponse>(result.Error, $"Failed to retrieve customer with ID {id}");
            }

            var response = result.Value.ToDto().ToGetResponse();

            LogSuccess("Customer retrieved successfully with ID {CustomerId}", id);
            return Result.Success(response);
        }
    }
}
