[{"ContainingType": "Program+<>c", "Method": "<<Main>$>b__0_2", "RelativePath": "api/customers", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "Publisher.Customer.Api.Features.Customers.GetCustomersResponse", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 400}], "EndpointName": "GetCustomers"}, {"ContainingType": "Program+<>c", "Method": "<<Main>$>b__0_6", "RelativePath": "api/customers", "HttpMethod": "POST", "IsController": false, "Order": 0, "Parameters": [{"Name": "request", "Type": "Publisher.Customer.Api.Features.Customers.CreateCustomerRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Publisher.Customer.Api.Features.Customers.CreateCustomerResponse", "MediaTypes": ["application/json"], "StatusCode": 201}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 400}], "EndpointName": "CreateCustomer"}, {"ContainingType": "Program+<>c", "Method": "<<Main>$>b__0_4", "RelativePath": "api/customers/{id:int}", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "Publisher.Customer.Api.Features.Customers.GetCustomerResponse", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}], "EndpointName": "GetCustomer"}, {"ContainingType": "Program+<>c", "Method": "<<Main>$>b__0_8", "RelativePath": "api/customers/{id:int}", "HttpMethod": "PUT", "IsController": false, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "request", "Type": "Publisher.Customer.Api.Features.Customers.UpdateCustomerRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "Publisher.Customer.Api.Features.Customers.UpdateCustomerResponse", "MediaTypes": ["application/json"], "StatusCode": 200}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 400}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}], "EndpointName": "UpdateCustomer"}, {"ContainingType": "Program+<>c", "Method": "<<Main>$>b__0_10", "RelativePath": "api/customers/{id:int}", "HttpMethod": "DELETE", "IsController": false, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 204}, {"Type": "System.Void", "MediaTypes": [], "StatusCode": 404}], "EndpointName": "DeleteCustomer"}]