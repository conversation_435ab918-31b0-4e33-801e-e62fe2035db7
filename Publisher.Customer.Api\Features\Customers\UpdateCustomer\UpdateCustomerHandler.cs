using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Publisher.Customer.Api.Domain;
using Publisher.Customer.Api.Infrastructure;

namespace Publisher.Customer.Api.Features.Customers.UpdateCustomer
{
    public class UpdateCustomerHandler : BaseCustomerHandler<UpdateCustomerHandler>
    {
        public UpdateCustomerHandler(ICustomerService customerService, ILogger<UpdateCustomerHandler> logger)
            : base(customerService, logger)
        {
        }

        public async Task<Result<UpdateCustomerResponse>> HandleAsync(int id, UpdateCustomerRequest request)
        {
            LogStart("Updating customer with ID {CustomerId}", id);

            var customer = request.ToDomain();
            var result = await CustomerService.UpdateCustomerAsync(id, customer);

            if (result.IsFailure)
            {
                return HandleFailure<UpdateCustomerResponse>(result.Error, $"Failed to update customer with ID {id}");
            }

            var response = result.Value.ToDto().ToUpdateResponse();

            LogSuccess("Customer updated successfully with ID {CustomerId}", id);
            return Result.Success(response);
        }
    }
}
