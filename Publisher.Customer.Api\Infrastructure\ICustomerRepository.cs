using System.Collections.Generic;
using System.Threading.Tasks;
using Publisher.Customer.Api.Domain;

namespace Publisher.Customer.Api.Infrastructure
{
    public interface ICustomerRepository
    {
        Task<Result<Domain.Customer>> GetByIdAsync(int id);
        Task<Result<IEnumerable<Domain.Customer>>> GetAllAsync();
        Task<Result<Domain.Customer>> CreateAsync(Domain.Customer customer);
        Task<Result<Domain.Customer>> UpdateAsync(Domain.Customer customer);
        Task<Result> DeleteAsync(int id);
        Task<bool> ExistsAsync(int id);
        Task<bool> EmailExistsAsync(string email, int? excludeId = null);
    }
}
