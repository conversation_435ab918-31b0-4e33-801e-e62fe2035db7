using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using FluentValidation;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using Publisher.Customer.Api.Extensions;
using Publisher.Customer.Api.Features.Customers;
using Publisher.Customer.Api.Features.Customers.CreateCustomer;
using Publisher.Customer.Api.Features.Customers.DeleteCustomer;
using Publisher.Customer.Api.Features.Customers.GetCustomer;
using Publisher.Customer.Api.Features.Customers.GetCustomers;
using Publisher.Customer.Api.Features.Customers.UpdateCustomer;
using Publisher.Customer.Api.Infrastructure;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new OpenApiInfo
    {
        Title = "Customer API",
        Version = "v1",
        Description = "A simple API for managing customers with Azure Service Bus integration"
    });
});

// Add application services
builder.Services.AddApplicationServices();
builder.Services.AddInfrastructure();

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "Customer API v1");
        c.RoutePrefix = string.Empty; // Serve Swagger UI at the app's root
        c.DisplayRequestDuration();
        c.EnableTryItOutByDefault();
    });
}

app.UseHttpsRedirection();

// Log Service Bus configuration
var logger = app.Services.GetRequiredService<ILogger<Program>>();
logger.LogInformation("Customer API started. Service Bus Topic: {TopicName}",
    app.Configuration["ServiceBus:CustomerTopicName"] ?? "customers");

// Customer API endpoints

// GET /api/customers
app.MapGet("/api/customers", async (HttpContext context) =>
{
    var handler = context.RequestServices.GetRequiredService<GetCustomersHandler>();
    var result = await handler.HandleAsync();

    if (result.IsSuccess)
    {
        return Results.Ok(result.Value);
    }
    else
    {
        return Results.BadRequest(new { error = result.Error });
    }
})
.WithName("GetCustomers")
.WithOpenApi(operation => new(operation)
{
    Summary = "Get all customers",
    Description = "Retrieves a list of all customers",
    Tags = new List<OpenApiTag> { new() { Name = "Customers" } }
})
.Produces<GetCustomersResponse>(200)
.Produces(400);

// GET /api/customers/{id}
app.MapGet("/api/customers/{id:int}", async (int id, GetCustomerHandler handler) =>
{
    var result = await handler.HandleAsync(id);

    if (result.IsSuccess)
    {
        return Results.Ok(result.Value);
    }
    else
    {
        return Results.NotFound(new { error = result.Error });
    }
})
.WithName("GetCustomer")
.WithOpenApi(operation => new(operation)
{
    Summary = "Get customer by ID",
    Description = "Retrieves a specific customer by their ID",
    Tags = new List<OpenApiTag> { new() { Name = "Customers" } }
})
.Produces<GetCustomerResponse>(200)
.Produces(404);

// POST /api/customers
app.MapPost("/api/customers", async (CreateCustomerRequest request, CreateCustomerHandler handler, IValidator<CreateCustomerRequest> validator) =>
{
    var validationResult = await validator.ValidateAsync(request);
    if (!validationResult.IsValid)
    {
        return Results.BadRequest(new {
            error = "Validation failed",
            details = validationResult.Errors.Select(e => new { field = e.PropertyName, message = e.ErrorMessage })
        });
    }

    var result = await handler.HandleAsync(request);

    if (result.IsSuccess)
    {
        return Results.Created($"/api/customers/{result.Value.Customer.Id}", result.Value);
    }
    else
    {
        return Results.BadRequest(new { error = result.Error });
    }
})
.WithName("CreateCustomer")
.WithOpenApi(operation => new(operation)
{
    Summary = "Create a new customer",
    Description = "Creates a new customer and publishes a customer created event",
    Tags = new List<OpenApiTag> { new() { Name = "Customers" } }
})
.Accepts<CreateCustomerRequest>("application/json")
.Produces<CreateCustomerResponse>(201)
.Produces(400);

// PUT /api/customers/{id}
app.MapPut("/api/customers/{id:int}", async (int id, UpdateCustomerRequest request, UpdateCustomerHandler handler, IValidator<UpdateCustomerRequest> validator) =>
{
    var validationResult = await validator.ValidateAsync(request);
    if (!validationResult.IsValid)
    {
        return Results.BadRequest(new {
            error = "Validation failed",
            details = validationResult.Errors.Select(e => new { field = e.PropertyName, message = e.ErrorMessage })
        });
    }

    var result = await handler.HandleAsync(id, request);

    if (result.IsSuccess)
    {
        return Results.Ok(result.Value);
    }
    else
    {
        return Results.NotFound(new { error = result.Error });
    }
})
.WithName("UpdateCustomer")
.WithOpenApi(operation => new(operation)
{
    Summary = "Update an existing customer",
    Description = "Updates an existing customer and publishes a customer updated event",
    Tags = new List<OpenApiTag> { new() { Name = "Customers" } }
})
.Accepts<UpdateCustomerRequest>("application/json")
.Produces<UpdateCustomerResponse>(200)
.Produces(400)
.Produces(404);

// DELETE /api/customers/{id}
app.MapDelete("/api/customers/{id:int}", async (int id, DeleteCustomerHandler handler) =>
{
    var result = await handler.HandleAsync(id);

    if (result.IsSuccess)
    {
        return Results.NoContent();
    }
    else
    {
        return Results.NotFound(new { error = result.Error });
    }
})
.WithName("DeleteCustomer")
.WithOpenApi(operation => new(operation)
{
    Summary = "Delete a customer",
    Description = "Deletes an existing customer and publishes a customer deleted event",
    Tags = new List<OpenApiTag> { new() { Name = "Customers" } }
})
.Produces(204)
.Produces(404);

app.Run();