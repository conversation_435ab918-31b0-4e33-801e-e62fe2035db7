using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Publisher.Customer.Api.Domain;
using Publisher.Customer.Api.Infrastructure;

namespace Publisher.Customer.Api.Features.Customers.GetCustomers
{
    public class GetCustomersHandler : BaseCustomerHandler<GetCustomersHandler>
    {
        public GetCustomersHandler(ICustomerService customerService, ILogger<GetCustomersHandler> logger)
            : base(customerService, logger)
        {
        }

        public async Task<Result<GetCustomersResponse>> HandleAsync()
        {
            LogStart("Retrieving all customers");

            var result = await CustomerService.GetAllCustomersAsync();

            if (result.IsFailure)
            {
                return HandleFailure<GetCustomersResponse>(result.Error, "Failed to retrieve customers");
            }

            var customerDtos = result.Value.ToDtos();
            var response = customerDtos.ToGetAllResponse();

            LogSuccess("Retrieved {CustomerCount} customers", customerDtos.Count());
            return Result.Success(response);
        }
    }
}
