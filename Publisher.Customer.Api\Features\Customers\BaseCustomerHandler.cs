using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Publisher.Customer.Api.Domain;
using Publisher.Customer.Api.Infrastructure;

namespace Publisher.Customer.Api.Features.Customers
{
    public abstract class BaseCustomerHandler<THandler>
    {
        protected readonly ICustomerService CustomerService;
        protected readonly ILogger<THandler> Logger;

        protected BaseCustomerHandler(ICustomerService customerService, ILogger<THandler> logger)
        {
            CustomerService = customerService;
            Logger = logger;
        }

        protected Result<TResponse> HandleFailure<TResponse>(string error, string context)
        {
            Logger.LogWarning("{Context}: {Error}", context, error);
            return Result.Failure<TResponse>(error);
        }

        protected Result HandleFailure(string error, string context)
        {
            Logger.LogWarning("{Context}: {Error}", context, error);
            return Result.Failure(error);
        }

        protected void LogSuccess(string message, params object[] args)
        {
            Logger.LogInformation(message, args);
        }

        protected void LogStart(string message, params object[] args)
        {
            Logger.LogInformation(message, args);
        }
    }
}
