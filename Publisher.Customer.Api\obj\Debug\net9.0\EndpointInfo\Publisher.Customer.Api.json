{"openapi": "3.0.1", "info": {"title": "Customer API", "description": "A simple API for managing customers with Azure Service Bus integration", "version": "v1"}, "paths": {"/api/customers": {"get": {"tags": ["Customers"], "summary": "Get all customers", "description": "Retrieves a list of all customers", "operationId": "GetCustomers", "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetCustomersResponse"}}}}, "400": {"description": "Bad Request"}}}, "post": {"tags": ["Customers"], "summary": "Create a new customer", "description": "Creates a new customer and publishes a customer created event", "operationId": "CreateCustomer", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCustomerRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateCustomerResponse"}}}}, "400": {"description": "Bad Request"}}}}, "/api/customers/{id}": {"get": {"tags": ["Customers"], "summary": "Get customer by ID", "description": "Retrieves a specific customer by their ID", "operationId": "GetCustomer", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetCustomerResponse"}}}}, "404": {"description": "Not Found"}}}, "put": {"tags": ["Customers"], "summary": "Update an existing customer", "description": "Updates an existing customer and publishes a customer updated event", "operationId": "UpdateCustomer", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCustomerRequest"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCustomerResponse"}}}}, "400": {"description": "Bad Request"}, "404": {"description": "Not Found"}}}, "delete": {"tags": ["Customers"], "summary": "Delete a customer", "description": "Deletes an existing customer and publishes a customer deleted event", "operationId": "DeleteCustomer", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {}}}, "204": {"description": "No Content"}, "404": {"description": "Not Found"}}}}}, "components": {"schemas": {"CreateCustomerRequest": {"type": "object", "properties": {"firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateCustomerResponse": {"type": "object", "properties": {"customer": {"$ref": "#/components/schemas/CustomerDto"}}, "additionalProperties": false}, "CustomerDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "GetCustomerResponse": {"type": "object", "properties": {"customer": {"$ref": "#/components/schemas/CustomerDto"}}, "additionalProperties": false}, "GetCustomersResponse": {"type": "object", "properties": {"customers": {"type": "array", "items": {"$ref": "#/components/schemas/CustomerDto"}, "nullable": true}}, "additionalProperties": false}, "UpdateCustomerRequest": {"type": "object", "properties": {"firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "phone": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateCustomerResponse": {"type": "object", "properties": {"customer": {"$ref": "#/components/schemas/CustomerDto"}}, "additionalProperties": false}}}}