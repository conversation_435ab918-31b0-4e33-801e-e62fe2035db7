using System;
using System.Collections.Generic;

namespace Publisher.Customer.Api.Features.Customers
{
    public record CustomerDto(
        int Id,
        string FirstName,
        string LastName,
        string Email,
        string Phone,
        DateTime CreatedAt,
        DateTime UpdatedAt
    );

    public record GetCustomersResponse(
        IEnumerable<CustomerDto> Customers
    );

    public record GetCustomerResponse(CustomerDto Customer);

    public record CreateCustomerResponse(CustomerDto Customer);

    public record UpdateCustomerResponse(CustomerDto Customer);
}
