using System.Collections.Generic;
using System.Threading.Tasks;
using Publisher.Customer.Api.Domain;

namespace Publisher.Customer.Api.Infrastructure
{
    public interface ICustomerService
    {
        Task<Result<Domain.Customer>> CreateCustomerAsync(Domain.Customer customer);
        Task<Result<Domain.Customer>> UpdateCustomerAsync(int id, Domain.Customer customer);
        Task<Result> DeleteCustomerAsync(int id);
        Task<Result<Domain.Customer>> GetCustomerByIdAsync(int id);
        Task<Result<IEnumerable<Domain.Customer>>> GetAllCustomersAsync();
    }
}
