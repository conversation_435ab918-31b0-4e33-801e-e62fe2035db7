using System.Threading.Tasks;
using Publisher.Customer.Api.Domain;
using Publisher.Customer.Api.Messages;

namespace Publisher.Customer.Api.Infrastructure
{
    public interface ICustomerMessagePublisher
    {
        Task<Result> PublishCustomerCreatedAsync(CustomerCreatedMessage message);
        Task<Result> PublishCustomerUpdatedAsync(CustomerUpdatedMessage message);
        Task<Result> PublishCustomerDeletedAsync(CustomerDeletedMessage message);
    }
}
