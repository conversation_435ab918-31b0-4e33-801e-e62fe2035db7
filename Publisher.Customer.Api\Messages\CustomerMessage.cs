using System;

namespace Publisher.Customer.Api.Messages
{
    public abstract class CustomerMessage
    {
        public int CustomerId { get; set; }
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public string CorrelationId { get; set; } = Guid.NewGuid().ToString();
        public abstract string MessageType { get; }
    }

    public class CustomerCreatedMessage : CustomerMessage
    {
        public override string MessageType => "CustomerCreated";
        public DateTime CreatedAt { get; set; }
    }

    public class CustomerUpdatedMessage : CustomerMessage
    {
        public override string MessageType => "CustomerUpdated";
        public DateTime UpdatedAt { get; set; }
    }

    public class CustomerDeletedMessage : CustomerMessage
    {
        public override string MessageType => "CustomerDeleted";
        public DateTime DeletedAt { get; set; }
    }
}
