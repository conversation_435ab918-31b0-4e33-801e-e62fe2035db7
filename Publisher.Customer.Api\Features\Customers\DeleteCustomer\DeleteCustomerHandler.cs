using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Publisher.Customer.Api.Domain;
using Publisher.Customer.Api.Infrastructure;

namespace Publisher.Customer.Api.Features.Customers.DeleteCustomer
{
    public class DeleteCustomerHandler : BaseCustomerHandler<DeleteCustomerHandler>
    {
        public DeleteCustomerHandler(ICustomerService customerService, ILogger<DeleteCustomerHandler> logger)
            : base(customerService, logger)
        {
        }

        public async Task<Result> HandleAsync(int id)
        {
            LogStart("Deleting customer with ID {CustomerId}", id);

            var result = await CustomerService.DeleteCustomerAsync(id);

            if (result.IsFailure)
            {
                return HandleFailure(result.Error, $"Failed to delete customer with ID {id}");
            }

            LogSuccess("Customer deleted successfully with ID {CustomerId}", id);
            return Result.Success();
        }
    }
}
