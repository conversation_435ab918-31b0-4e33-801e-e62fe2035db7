using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Publisher.Customer.Api.Domain;

namespace Publisher.Customer.Api.Infrastructure
{
    public class CustomerRepository : ICustomerRepository
    {
        private readonly CustomerDbContext _context;
        private readonly ILogger<CustomerRepository> _logger;

        public CustomerRepository(CustomerDbContext context, ILogger<CustomerRepository> logger)
        {
            _context = context;
            _logger = logger;
        }

    public async Task<Result<Domain.Customer>> GetByIdAsync(int id)
    {
        try
        {
            var customer = await _context.Customers.FindAsync(id);

            if (customer == null)
            {
                _logger.LogWarning("Customer with ID {CustomerId} not found", id);
                return Result.Failure<Domain.Customer>($"Customer with ID {id} not found");
            }

            return Result.Success(customer);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving customer with ID {CustomerId}", id);
            return Result.Failure<Domain.Customer>("An error occurred while retrieving the customer");
        }
    }

    public async Task<Result<IEnumerable<Domain.Customer>>> GetAllAsync()
    {
        try
        {
            var customers = await _context.Customers
                .OrderBy(c => c.LastName)
                .ThenBy(c => c.FirstName)
                .ToListAsync();

            return Result.Success<IEnumerable<Domain.Customer>>(customers);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving all customers");
            return Result.Failure<IEnumerable<Domain.Customer>>("An error occurred while retrieving customers");
        }
    }

    public async Task<Result<Domain.Customer>> CreateAsync(Domain.Customer customer)
    {
        try
        {
            if (await EmailExistsAsync(customer.Email))
            {
                _logger.LogWarning("Attempt to create customer with existing email {Email}", customer.Email);
                return Result.Failure<Domain.Customer>("A customer with this email already exists");
            }

            _context.Customers.Add(customer);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Customer created with ID {CustomerId}", customer.Id);
            return Result.Success(customer);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating customer with email {Email}", customer.Email);
            return Result.Failure<Domain.Customer>("An error occurred while creating the customer");
        }
    }

    public async Task<Result<Domain.Customer>> UpdateAsync(Domain.Customer customer)
    {
        try
        {
            if (await EmailExistsAsync(customer.Email, customer.Id))
            {
                _logger.LogWarning("Attempt to update customer {CustomerId} with existing email {Email}", customer.Id, customer.Email);
                return Result.Failure<Domain.Customer>("A customer with this email already exists");
            }

            _context.Customers.Update(customer);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Customer updated with ID {CustomerId}", customer.Id);
            return Result.Success(customer);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating customer with ID {CustomerId}", customer.Id);
            return Result.Failure<Domain.Customer>("An error occurred while updating the customer");
        }
    }

    public async Task<Result> DeleteAsync(int id)
    {
        try
        {
            var customer = await _context.Customers.FindAsync(id);

            if (customer == null)
            {
                _logger.LogWarning("Attempt to delete non-existent customer with ID {CustomerId}", id);
                return Result.Failure($"Customer with ID {id} not found");
            }

            _context.Customers.Remove(customer);
            await _context.SaveChangesAsync();

            _logger.LogInformation("Customer deleted with ID {CustomerId}", id);
            return Result.Success();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting customer with ID {CustomerId}", id);
            return Result.Failure("An error occurred while deleting the customer");
        }
    }

    public async Task<bool> ExistsAsync(int id)
    {
        try
        {
            return await _context.Customers.AnyAsync(c => c.Id == id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if customer exists with ID {CustomerId}", id);
            return false;
        }
    }

    public async Task<bool> EmailExistsAsync(string email, int? excludeId = null)
    {
        try
        {
            var query = _context.Customers.Where(c => c.Email == email);

            if (excludeId.HasValue)
            {
                query = query.Where(c => c.Id != excludeId.Value);
            }

            return await query.AnyAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if email exists {Email}", email);
            return false;
        }
    }
}
}
