using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Publisher.Customer.Api.Domain;
using Publisher.Customer.Api.Infrastructure;

namespace Publisher.Customer.Api.Features.Customers.CreateCustomer
{
    public class CreateCustomerHandler : BaseCustomerHandler<CreateCustomerHandler>
    {
        public CreateCustomerHandler(ICustomerService customerService, ILogger<CreateCustomerHandler> logger)
            : base(customerService, logger)
        {
        }

        public async Task<Result<CreateCustomerResponse>> HandleAsync(CreateCustomerRequest request)
        {
            LogStart("Creating customer with email {Email}", request.Email);

            var customer = request.ToDomain();
            var result = await CustomerService.CreateCustomerAsync(customer);

            if (result.IsFailure)
            {
                return HandleFailure<CreateCustomerResponse>(result.Error, "Failed to create customer");
            }

            var response = result.Value.ToDto().ToCreateResponse();

            LogSuccess("Customer created successfully with ID {CustomerId}", result.Value.Id);
            return Result.Success(response);
        }
    }
}
