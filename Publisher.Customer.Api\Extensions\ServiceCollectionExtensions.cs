using FluentValidation;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Publisher.Customer.Api.Features.Customers;
using Publisher.Customer.Api.Features.Customers.CreateCustomer;
using Publisher.Customer.Api.Features.Customers.DeleteCustomer;
using Publisher.Customer.Api.Features.Customers.GetCustomer;
using Publisher.Customer.Api.Features.Customers.GetCustomers;
using Publisher.Customer.Api.Features.Customers.UpdateCustomer;
using Publisher.Customer.Api.Infrastructure;

namespace Publisher.Customer.Api.Extensions
{
    public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddApplicationServices(this IServiceCollection services)
    {
        // Add handlers
        services.AddScoped<CreateCustomerHandler>();
        services.AddScoped<GetCustomerHandler>();
        services.AddScoped<GetCustomersHandler>();
        services.AddScoped<UpdateCustomerHandler>();
        services.AddScoped<DeleteCustomerHandler>();

        // Add validators
        services.AddScoped<IValidator<CreateCustomerRequest>, CreateCustomerValidator>();
        services.AddScoped<IValidator<UpdateCustomerRequest>, UpdateCustomerValidator>();

        return services;
    }

    public static IServiceCollection AddInfrastructure(this IServiceCollection services)
    {
        // Add Service Bus message publisher
        services.AddSingleton<ICustomerMessagePublisher, ServiceBusCustomerMessagePublisher>();

        // Add customer service
        services.AddScoped<ICustomerService, CustomerService>();

        return services;
    }
}
}
