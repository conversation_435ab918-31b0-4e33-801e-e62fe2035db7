using System.Collections.Generic;
using System.Linq;

namespace Publisher.Customer.Api.Features.Customers
{
    public static class CustomerMappingExtensions
    {
        public static CustomerDto ToDto(this Domain.Customer customer)
        {
            return new CustomerDto(
                customer.Id,
                customer.FirstName,
                customer.LastName,
                customer.Email,
                customer.Phone,
                customer.CreatedAt,
                customer.UpdatedAt
            );
        }

        public static IEnumerable<CustomerDto> ToDtos(this IEnumerable<Domain.Customer> customers)
        {
            return customers.Select(customer => customer.ToDto());
        }

        public static Domain.Customer ToDomain(this CreateCustomerRequest request)
        {
            return new Domain.Customer
            {
                FirstName = request.FirstName.Trim(),
                LastName = request.LastName.Trim(),
                Email = request.Email.Trim().ToLowerInvariant(),
                Phone = request.Phone.Trim()
            };
        }

        public static Domain.Customer ToDomain(this UpdateCustomerRequest request)
        {
            return new Domain.Customer
            {
                FirstName = request.FirstName.Trim(),
                LastName = request.LastName.Trim(),
                Email = request.Email.Trim().ToLowerInvariant(),
                Phone = request.Phone.Trim()
            };
        }

        public static GetCustomerResponse ToGetResponse(this CustomerDto dto)
        {
            return new GetCustomerResponse(dto);
        }

        public static CreateCustomerResponse ToCreateResponse(this CustomerDto dto)
        {
            return new CreateCustomerResponse(dto);
        }

        public static UpdateCustomerResponse ToUpdateResponse(this CustomerDto dto)
        {
            return new UpdateCustomerResponse(dto);
        }

        public static GetCustomersResponse ToGetAllResponse(this IEnumerable<CustomerDto> dtos)
        {
            return new GetCustomersResponse(dtos);
        }
    }
}
